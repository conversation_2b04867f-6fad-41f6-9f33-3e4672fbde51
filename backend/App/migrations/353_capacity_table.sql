BEGIN;

CREATE table if not exists public."cl_tx_capacity" (
	"db_id" bigserial NOT NULL,
	"usr_tmzone_day" date NOT NULL,
	"resource_id" varchar(100) NOT NULL,
	"resource_label" varchar(250) not null,
	"start_time" timestamp NOT NULL,
	"end_time" timestamp NOT NULL,
	"total_capacity" int4 NOT NULL,
	"available_capacity" int4 NOT NULL,
	"booked_capacity" int4 not null,
	"c_meta" public.entry_c_meta NULL,
	"u_meta" public.entry_c_meta NULL,
	CONSTRAINT "capacity_pkey" PRIMARY KEY (db_id)
);
CREATE index if not exists capacity_resource_id_day ON public."cl_tx_capacity" USING btree ("resource_id", "usr_tmzone_day");
CREATE UNIQUE INDEX if not exists capacity_resource_id_start_time_end_time ON public."cl_tx_capacity" USING btree ("resource_id", "start_time", "end_time");


END;