-- Drop existing versions of the function
DROP FUNCTION IF EXISTS public.tms_ace_bulk_upsert_capacity_data(json, integer, text, text);
DROP FUNCTION IF EXISTS public.tms_ace_bulk_upsert_capacity_data(json, integer);

CREATE OR REPLACE FUNCTION public.tms_ace_bulk_upsert_capacity_data(
    p_capacity_records json,
    p_org_id integer,
    p_ip_address text DEFAULT NULL,
    p_user_agent text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql AS $function$
DECLARE
    total_records integer := 0;
    success_count integer := 0;
    failure_count integer := 0;
    
BEGIN
    -- Validate input parameters
    IF p_capacity_records IS NULL OR json_array_length(p_capacity_records) = 0 THEN
        RETURN json_build_object(
            'status', false,
            'message', 'No capacity records provided',
            'data', json_build_object(
                'total_records', 0,
                'success_count', 0,
                'failure_count', 0
            ),
            'timestamp', now()
        );
    END IF;

    -- Check if the organization exists
    IF NOT EXISTS (SELECT 1 FROM public.cl_tx_orgs WHERE org_id = p_org_id) THEN
        RETURN json_build_object(
            'status', false,
            'message', 'Organization not found',
            'data', json_build_object(
                'org_id', p_org_id,
                'total_records', 0,
                'success_count', 0,
                'failure_count', 0
            ),
            'timestamp', now()
        );
    END IF;

    -- Get total number of records
    total_records := json_array_length(p_capacity_records);

    -- Use a single bulk INSERT with ON CONFLICT for better performance
    -- This approach is much faster than processing records one by one
    BEGIN
        WITH capacity_data AS (
            SELECT
                (record->>'resourceId')::varchar(100) as resource_id,
                COALESCE(
                    (record->>'organizationName') || '_' ||
                    (record->'metadata'->>'verticalName') || '_' ||
                    (record->'metadata'->>'skillName') || '_' ||
                    (record->'metadata'->>'hubCode'),
                    'Resource: ' || (record->>'resourceId')
                ) as resource_label,
                (record->>'startTime')::timestamp as start_time,
                (record->>'endTime')::timestamp as end_time,
                (record->>'startTime')::date as usr_tmzone_day,
                (record->>'totalCapacity')::integer as total_capacity,
                (record->>'availableCapacity')::integer as available_capacity,
                (record->>'totalCapacity')::integer - (record->>'availableCapacity')::integer as booked_capacity
            FROM json_array_elements(p_capacity_records) as record
            WHERE 
                record->>'resourceId' IS NOT NULL 
                AND record->>'resourceId' != ''
                AND record->>'startTime' IS NOT NULL
                AND record->>'endTime' IS NOT NULL
                AND record->>'totalCapacity' IS NOT NULL
                AND record->>'availableCapacity' IS NOT NULL
                AND (record->>'totalCapacity')::integer >= 0
                AND (record->>'availableCapacity')::integer >= 0
                AND (record->>'startTime')::timestamp < (record->>'endTime')::timestamp
        )
        INSERT INTO public.cl_tx_capacity (
            usr_tmzone_day,
            resource_id,
            resource_label,
            start_time,
            end_time,
            total_capacity,
            available_capacity,
            booked_capacity,
            c_meta,
            u_meta
        )
        SELECT 
            usr_tmzone_day,
            resource_id,
            resource_label,
            start_time,
            end_time,
            total_capacity,
            available_capacity,
            booked_capacity,
            row(p_ip_address, p_user_agent, now() at time zone 'utc'),
            row(p_ip_address, p_user_agent, now() at time zone 'utc')
        FROM capacity_data
        ON CONFLICT (resource_id, start_time, end_time)
        DO UPDATE SET
            usr_tmzone_day = EXCLUDED.usr_tmzone_day,
            resource_label = EXCLUDED.resource_label,
            total_capacity = EXCLUDED.total_capacity,
            available_capacity = EXCLUDED.available_capacity,
            booked_capacity = EXCLUDED.booked_capacity,
            u_meta = row(p_ip_address, p_user_agent, now() at time zone 'utc');

        -- Get the number of rows affected (this includes both inserts and updates)
        GET DIAGNOSTICS success_count = ROW_COUNT;
        
        -- Calculate failure count (records that didn't pass validation)
        failure_count := total_records - success_count;

    EXCEPTION
        WHEN OTHERS THEN
            -- If bulk operation fails, fall back to the record-by-record function
            RETURN public.tms_ace_batch_upsert_capacity_data(
                p_capacity_records, 
                p_org_id, 
                p_ip_address, 
                p_user_agent
            );
    END;

    -- Return the results
    RETURN json_build_object(
        'status', failure_count = 0,
        'message', CASE 
            WHEN failure_count = 0 THEN 
                format('Successfully processed %s capacity records using bulk operation', success_count)
            ELSE 
                format('Bulk processed %s records with %s validation failures out of %s total records', 
                       success_count, failure_count, total_records)
        END,
        'data', json_build_object(
            'total_records', total_records,
            'success_count', success_count,
            'failure_count', failure_count,
            'processing_method', 'bulk_operation'
        ),
        'timestamp', now()
    );

END;
$function$;
