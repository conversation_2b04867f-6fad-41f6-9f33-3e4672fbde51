CREATE OR REPLACE FUNCTION public.tms_ace_batch_upsert_capacity_data(
    p_capacity_records json,
    p_org_id integer,
    p_ip_address text DEFAULT NULL,
    p_user_agent text DEFAULT NULL
)
RETURNS json
LANGUAGE plpgsql AS $function$
DECLARE
    capacity_record json;
    resource_id_ varchar(100);
    resource_label_ varchar(250);
    start_time_ timestamp;
    end_time_ timestamp;
    usr_tmzone_day_ date;
    total_capacity_ integer;
    available_capacity_ integer;
    booked_capacity_ integer;
    
    success_count integer := 0;
    failure_count integer := 0;
    total_records integer := 0;
    error_details json := '[]'::json;
    
    current_error_detail json;
    
BEGIN
    -- Validate input parameters
    IF p_capacity_records IS NULL OR json_array_length(p_capacity_records) = 0 THEN
        RETURN json_build_object(
            'status', false,
            'message', 'No capacity records provided',
            'data', json_build_object(
                'total_records', 0,
                'success_count', 0,
                'failure_count', 0
            ),
            'timestamp', now()
        );
    END IF;

    -- Check if the organization exists
    IF NOT EXISTS (SELECT 1 FROM public.cl_tx_orgs WHERE org_id = p_org_id) THEN
        RETURN json_build_object(
            'status', false,
            'message', 'Organization not found',
            'data', json_build_object(
                'org_id', p_org_id,
                'total_records', 0,
                'success_count', 0,
                'failure_count', 0
            ),
            'timestamp', now()
        );
    END IF;

    -- Get total number of records
    total_records := json_array_length(p_capacity_records);

    -- Process each capacity record
    FOR i IN 0..(total_records - 1) LOOP
        BEGIN
            -- Extract the current record
            capacity_record := p_capacity_records->i;
            
            -- Extract and validate required fields
            resource_id_ := capacity_record->>'resourceId';
            start_time_ := (capacity_record->>'startTime')::timestamp;
            end_time_ := (capacity_record->>'endTime')::timestamp;
            total_capacity_ := (capacity_record->>'totalCapacity')::integer;
            available_capacity_ := (capacity_record->>'availableCapacity')::integer;
            
            -- Set booked_capacity as the difference between total and available
            booked_capacity_ := total_capacity_ - available_capacity_;
            
            -- Extract day from start_time for usr_tmzone_day
            usr_tmzone_day_ := start_time_::date;
            
            -- Generate resource_label from resource_id (can be customized as needed)
            resource_label_ := 'Resource: ' || resource_id_;

            -- Validate required fields
            IF resource_id_ IS NULL OR resource_id_ = '' THEN
                RAISE EXCEPTION 'resourceId is required for record %', i + 1;
            END IF;
            
            IF start_time_ IS NULL THEN
                RAISE EXCEPTION 'startTime is required for record %', i + 1;
            END IF;
            
            IF end_time_ IS NULL THEN
                RAISE EXCEPTION 'endTime is required for record %', i + 1;
            END IF;
            
            IF total_capacity_ IS NULL OR total_capacity_ < 0 THEN
                RAISE EXCEPTION 'totalCapacity must be a non-negative integer for record %', i + 1;
            END IF;
            
            IF available_capacity_ IS NULL OR available_capacity_ < 0 THEN
                RAISE EXCEPTION 'availableCapacity must be a non-negative integer for record %', i + 1;
            END IF;
            
            IF start_time_ >= end_time_ THEN
                RAISE EXCEPTION 'startTime must be before endTime for record %', i + 1;
            END IF;

            -- Insert or update the capacity record using ON CONFLICT
            INSERT INTO public.cl_tx_capacity (
                usr_tmzone_day,
                resource_id,
                resource_label,
                start_time,
                end_time,
                total_capacity,
                available_capacity,
                booked_capacity,
                c_meta,
                u_meta
            ) VALUES (
                usr_tmzone_day_,
                resource_id_,
                resource_label_,
                start_time_,
                end_time_,
                total_capacity_,
                available_capacity_,
                booked_capacity_,
                row(p_ip_address, p_user_agent, now() at time zone 'utc'),
                row(p_ip_address, p_user_agent, now() at time zone 'utc')
            )
            ON CONFLICT (resource_id, start_time, end_time)
            DO UPDATE SET
                usr_tmzone_day = EXCLUDED.usr_tmzone_day,
                resource_label = EXCLUDED.resource_label,
                total_capacity = EXCLUDED.total_capacity,
                available_capacity = EXCLUDED.available_capacity,
                booked_capacity = EXCLUDED.booked_capacity,
                u_meta = row(p_ip_address, p_user_agent, now() at time zone 'utc');

            -- Increment success count
            success_count := success_count + 1;

        EXCEPTION
            WHEN OTHERS THEN
                -- Increment failure count
                failure_count := failure_count + 1;
                
                -- Build error detail for this record
                current_error_detail := json_build_object(
                    'record_index', i + 1,
                    'resource_id', COALESCE(resource_id_, 'unknown'),
                    'error_message', SQLERRM,
                    'error_code', SQLSTATE
                );
                
                -- Add to error details array
                error_details := error_details || current_error_detail;
        END;
    END LOOP;

    -- Return the results
    RETURN json_build_object(
        'status', failure_count = 0,
        'message', CASE 
            WHEN failure_count = 0 THEN 
                format('Successfully processed %s capacity records', success_count)
            ELSE 
                format('Processed %s records with %s failures out of %s total records', 
                       success_count, failure_count, total_records)
        END,
        'data', json_build_object(
            'total_records', total_records,
            'success_count', success_count,
            'failure_count', failure_count,
            'error_details', CASE WHEN failure_count > 0 THEN error_details ELSE NULL END
        ),
        'timestamp', now()
    );

END;
$function$;
